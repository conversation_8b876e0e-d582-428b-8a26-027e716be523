<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ذكرى الإمام محمد الباقر عليه السلام - AliToucan</title>
    
    <!-- Arabic Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Scheherazade+New:wght@400;700&family=Noto+Sans+Arabic:wght@400;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Amiri', 'Scheherazade New', 'Noto Sans Arabic', serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 20px;
            color: #d4af37;
        }

        .container {
            max-width: 600px;
            width: 100%;
            text-align: center;
            margin-bottom: 30px;
        }

        .title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            color: #d4af37;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
            font-family: 'Amiri', serif;
        }

        .subtitle {
            font-size: 1.2rem;
            margin-bottom: 30px;
            color: #b8860b;
            font-family: 'Scheherazade New', serif;
        }

        /* Condolence Design Canvas */
        .design-container {
            position: relative;
            width: 500px;
            height: 500px;
            margin: 0 auto 30px;
            background: linear-gradient(135deg, #000000 0%, #1a1a2e 50%, #0f3460 100%);
            border: 3px solid #d4af37;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.8);
            overflow: hidden;
        }

        .islamic-pattern {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                radial-gradient(circle at 25% 25%, rgba(212, 175, 55, 0.1) 2px, transparent 2px),
                radial-gradient(circle at 75% 75%, rgba(212, 175, 55, 0.1) 2px, transparent 2px);
            background-size: 50px 50px;
            opacity: 0.3;
        }

        .geometric-border {
            position: absolute;
            top: 15px;
            left: 15px;
            right: 15px;
            bottom: 15px;
            border: 2px solid #d4af37;
            border-radius: 10px;
            background: rgba(212, 175, 55, 0.05);
        }

        .content-area {
            position: relative;
            z-index: 10;
            padding: 40px 30px;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: center;
            text-align: center;
        }

        .imam-name {
            font-family: 'Amiri', serif;
            font-size: 2.2rem;
            font-weight: 700;
            color: #d4af37;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.9);
            margin-bottom: 15px;
            line-height: 1.3;
        }

        .peace-phrase {
            font-family: 'Scheherazade New', serif;
            font-size: 1.1rem;
            color: #b8860b;
            margin-bottom: 20px;
        }

        .condolence-text {
            font-family: 'Noto Sans Arabic', sans-serif;
            font-size: 1.3rem;
            color: #ffffff;
            text-shadow: 1px 1px 3px rgba(0,0,0,0.8);
            margin-bottom: 15px;
            line-height: 1.5;
        }

        .prayer-text {
            font-family: 'Amiri', serif;
            font-size: 1rem;
            color: #d4af37;
            font-style: italic;
            margin-bottom: 20px;
            line-height: 1.4;
        }

        .branding {
            position: absolute;
            bottom: 10px;
            right: 15px;
            font-family: 'Noto Sans Arabic', sans-serif;
            font-size: 0.8rem;
            color: #888;
            opacity: 0.7;
        }

        /* Control Panel */
        .controls {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
            margin-bottom: 20px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-family: 'Noto Sans Arabic', sans-serif;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: linear-gradient(135deg, #d4af37, #b8860b);
            color: #000;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #b8860b, #d4af37);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(212, 175, 55, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            color: #d4af37;
            border: 2px solid #d4af37;
        }

        .btn-secondary:hover {
            background: linear-gradient(135deg, #d4af37, #b8860b);
            color: #000;
            transform: translateY(-2px);
        }

        /* Loading Animation */
        .loading {
            display: none;
            text-align: center;
            color: #d4af37;
            font-size: 1.1rem;
            margin-top: 10px;
        }

        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(212, 175, 55, 0.3);
            border-radius: 50%;
            border-top-color: #d4af37;
            animation: spin 1s ease-in-out infinite;
            margin-left: 10px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .design-container {
                width: 90vw;
                height: 90vw;
                max-width: 400px;
                max-height: 400px;
            }
            
            .title {
                font-size: 2rem;
            }
            
            .imam-name {
                font-size: 1.8rem;
            }
            
            .condolence-text {
                font-size: 1.1rem;
            }
            
            .controls {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 200px;
            }
        }

        @media (max-width: 480px) {
            .design-container {
                width: 95vw;
                height: 95vw;
            }
            
            .content-area {
                padding: 25px 20px;
            }
            
            .imam-name {
                font-size: 1.5rem;
            }
            
            .condolence-text {
                font-size: 1rem;
            }
        }

        /* Islamic Decorative Elements */
        .decorative-corner {
            position: absolute;
            width: 40px;
            height: 40px;
            border: 2px solid #d4af37;
        }

        .corner-top-left {
            top: 25px;
            left: 25px;
            border-right: none;
            border-bottom: none;
        }

        .corner-top-right {
            top: 25px;
            right: 25px;
            border-left: none;
            border-bottom: none;
        }

        .corner-bottom-left {
            bottom: 25px;
            left: 25px;
            border-right: none;
            border-top: none;
        }

        .corner-bottom-right {
            bottom: 25px;
            right: 25px;
            border-left: none;
            border-top: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">ذكرى استشهاد الإمام محمد الباقر</h1>
        <p class="subtitle">عليه أفضل الصلاة والسلام</p>
    </div>

    <!-- Condolence Design -->
    <div class="design-container" id="condolenceDesign">
        <div class="islamic-pattern"></div>
        <div class="geometric-border"></div>

        <!-- Decorative Corners -->
        <div class="decorative-corner corner-top-left"></div>
        <div class="decorative-corner corner-top-right"></div>
        <div class="decorative-corner corner-bottom-left"></div>
        <div class="decorative-corner corner-bottom-right"></div>

        <div class="content-area">
            <div>
                <div class="imam-name">الإمام محمد الباقر</div>
                <div class="peace-phrase">عليه السلام</div>
                <div class="condolence-text">عظم الله أجوركم بذكرى استشهاد</div>
                <div class="condolence-text">باقر علوم الأولين والآخرين</div>
            </div>

            <div>
                <div class="prayer-text">اللهم صل على محمد وآل محمد</div>
                <div class="prayer-text">وعجل فرجهم الشريف</div>
            </div>
        </div>

        <div class="branding">AliToucan</div>
    </div>

    <!-- Control Panel -->
    <div class="controls">
        <button class="btn btn-primary" onclick="downloadImage()">تحميل الصورة</button>
        <button class="btn btn-secondary" onclick="shareDesign()">مشاركة التصميم</button>
        <button class="btn btn-secondary" onclick="previewDesign()">معاينة</button>
    </div>

    <div class="loading" id="loadingIndicator">
        جاري تحضير الصورة...
        <div class="spinner"></div>
    </div>

    <!-- Hidden Canvas for Image Generation -->
    <canvas id="hiddenCanvas" style="display: none;"></canvas>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <script>
        // Enhanced image download functionality
        async function downloadImage() {
            const loadingIndicator = document.getElementById('loadingIndicator');
            const designContainer = document.getElementById('condolenceDesign');

            try {
                // Show loading indicator
                loadingIndicator.style.display = 'block';

                // Configure html2canvas options for high quality
                const options = {
                    canvas: document.getElementById('hiddenCanvas'),
                    scale: 3, // High resolution
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: null,
                    width: 500,
                    height: 500,
                    scrollX: 0,
                    scrollY: 0,
                    windowWidth: 500,
                    windowHeight: 500
                };

                // Generate canvas from the design
                const canvas = await html2canvas(designContainer, options);

                // Create download link
                const link = document.createElement('a');
                link.download = 'الإمام_محمد_الباقر_عليه_السلام_تعزية.png';
                link.href = canvas.toDataURL('image/png', 1.0);

                // Trigger download
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                // Show success message
                showNotification('تم تحميل الصورة بنجاح!', 'success');

            } catch (error) {
                console.error('Error generating image:', error);
                showNotification('حدث خطأ في تحميل الصورة. يرجى المحاولة مرة أخرى.', 'error');
            } finally {
                // Hide loading indicator
                loadingIndicator.style.display = 'none';
            }
        }

        // Share functionality
        async function shareDesign() {
            if (navigator.share) {
                try {
                    await navigator.share({
                        title: 'ذكرى استشهاد الإمام محمد الباقر عليه السلام',
                        text: 'عظم الله أجوركم بذكرى استشهاد باقر علوم الأولين والآخرين',
                        url: window.location.href
                    });
                } catch (error) {
                    console.log('Error sharing:', error);
                    copyToClipboard();
                }
            } else {
                copyToClipboard();
            }
        }

        // Copy URL to clipboard
        function copyToClipboard() {
            navigator.clipboard.writeText(window.location.href).then(() => {
                showNotification('تم نسخ الرابط إلى الحافظة!', 'success');
            }).catch(() => {
                showNotification('لم يتم نسخ الرابط. يرجى النسخ يدوياً.', 'error');
            });
        }

        // Preview functionality
        function previewDesign() {
            const designContainer = document.getElementById('condolenceDesign');

            // Create modal for preview
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.9);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 1000;
                cursor: pointer;
            `;

            const previewImage = designContainer.cloneNode(true);
            previewImage.style.cssText = `
                transform: scale(1.2);
                box-shadow: 0 20px 60px rgba(212, 175, 55, 0.3);
            `;

            modal.appendChild(previewImage);
            document.body.appendChild(modal);

            // Close modal on click
            modal.addEventListener('click', () => {
                document.body.removeChild(modal);
            });

            // Close modal on escape key
            const handleEscape = (e) => {
                if (e.key === 'Escape') {
                    document.body.removeChild(modal);
                    document.removeEventListener('keydown', handleEscape);
                }
            };
            document.addEventListener('keydown', handleEscape);
        }

        // Notification system
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 20px;
                border-radius: 8px;
                color: white;
                font-family: 'Noto Sans Arabic', sans-serif;
                font-weight: 600;
                z-index: 1001;
                transform: translateX(100%);
                transition: transform 0.3s ease;
                max-width: 300px;
                text-align: right;
                direction: rtl;
            `;

            // Set background color based on type
            switch (type) {
                case 'success':
                    notification.style.background = 'linear-gradient(135deg, #28a745, #20c997)';
                    break;
                case 'error':
                    notification.style.background = 'linear-gradient(135deg, #dc3545, #e74c3c)';
                    break;
                default:
                    notification.style.background = 'linear-gradient(135deg, #d4af37, #b8860b)';
                    notification.style.color = '#000';
            }

            notification.textContent = message;
            document.body.appendChild(notification);

            // Animate in
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);

            // Remove after 3 seconds
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Add smooth animations
            const designContainer = document.getElementById('condolenceDesign');
            designContainer.style.opacity = '0';
            designContainer.style.transform = 'translateY(20px)';

            setTimeout(() => {
                designContainer.style.transition = 'all 0.8s ease';
                designContainer.style.opacity = '1';
                designContainer.style.transform = 'translateY(0)';
            }, 300);

            // Add keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey || e.metaKey) {
                    switch (e.key) {
                        case 's':
                            e.preventDefault();
                            downloadImage();
                            break;
                        case 'p':
                            e.preventDefault();
                            previewDesign();
                            break;
                    }
                }
            });

            showNotification('مرحباً بكم في تصميم تعزية الإمام محمد الباقر عليه السلام', 'info');
        });

        // Enhanced responsive handling
        function handleResize() {
            const designContainer = document.getElementById('condolenceDesign');
            const containerWidth = Math.min(window.innerWidth - 40, 500);

            if (window.innerWidth <= 768) {
                designContainer.style.width = containerWidth + 'px';
                designContainer.style.height = containerWidth + 'px';
            }
        }

        window.addEventListener('resize', handleResize);
        window.addEventListener('load', handleResize);
    </script>
</body>
</html>
