<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ذكرى الإمام محمد الباقر عليه السلام - AliToucan</title>
    
    <!-- Arabic Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Scheherazade+New:wght@400;700&family=Noto+Sans+Arabic:wght@400;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Amiri', 'Scheherazade New', 'Noto Sans Arabic', serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 20px;
            color: #d4af37;
        }

        .container {
            max-width: 600px;
            width: 100%;
            text-align: center;
            margin-bottom: 30px;
        }

        .title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            color: #d4af37;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
            font-family: 'Amiri', serif;
        }

        .subtitle {
            font-size: 1.2rem;
            margin-bottom: 30px;
            color: #b8860b;
            font-family: 'Scheherazade New', serif;
        }

        /* Condolence Design Canvas */
        .design-container {
            position: relative;
            width: 500px;
            height: 500px;
            margin: 0 auto 30px;
            background: linear-gradient(135deg, #000000 0%, #1a1a2e 50%, #0f3460 100%);
            border: 3px solid #d4af37;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.8);
            overflow: hidden;
        }

        .islamic-pattern {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                radial-gradient(circle at 25% 25%, rgba(212, 175, 55, 0.1) 2px, transparent 2px),
                radial-gradient(circle at 75% 75%, rgba(212, 175, 55, 0.1) 2px, transparent 2px);
            background-size: 50px 50px;
            opacity: 0.3;
        }

        .geometric-border {
            position: absolute;
            top: 15px;
            left: 15px;
            right: 15px;
            bottom: 15px;
            border: 2px solid #d4af37;
            border-radius: 10px;
            background: rgba(212, 175, 55, 0.05);
        }

        .content-area {
            position: relative;
            z-index: 10;
            padding: 40px 30px;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: center;
            text-align: center;
        }

        .imam-name {
            font-family: 'Amiri', serif;
            font-size: 2.2rem;
            font-weight: 700;
            color: #d4af37;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.9);
            margin-bottom: 15px;
            line-height: 1.3;
        }

        .peace-phrase {
            font-family: 'Scheherazade New', serif;
            font-size: 1.1rem;
            color: #b8860b;
            margin-bottom: 20px;
        }

        .condolence-text {
            font-family: 'Noto Sans Arabic', sans-serif;
            font-size: 1.3rem;
            color: #ffffff;
            text-shadow: 1px 1px 3px rgba(0,0,0,0.8);
            margin-bottom: 15px;
            line-height: 1.5;
        }

        .prayer-text {
            font-family: 'Amiri', serif;
            font-size: 1rem;
            color: #d4af37;
            font-style: italic;
            margin-bottom: 20px;
            line-height: 1.4;
        }



        /* Control Panel */
        .controls {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
            margin-bottom: 20px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-family: 'Noto Sans Arabic', sans-serif;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: linear-gradient(135deg, #d4af37, #b8860b);
            color: #000;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #b8860b, #d4af37);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(212, 175, 55, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            color: #d4af37;
            border: 2px solid #d4af37;
        }

        .btn-secondary:hover {
            background: linear-gradient(135deg, #d4af37, #b8860b);
            color: #000;
            transform: translateY(-2px);
        }

        /* Loading Animation */
        .loading {
            display: none;
            text-align: center;
            color: #d4af37;
            font-size: 1.1rem;
            margin-top: 10px;
        }

        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(212, 175, 55, 0.3);
            border-radius: 50%;
            border-top-color: #d4af37;
            animation: spin 1s ease-in-out infinite;
            margin-left: 10px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .design-container {
                width: 90vw;
                height: 90vw;
                max-width: 400px;
                max-height: 400px;
            }
            
            .title {
                font-size: 2rem;
            }
            
            .imam-name {
                font-size: 1.8rem;
            }
            
            .condolence-text {
                font-size: 1.1rem;
            }
            
            .controls {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 200px;
            }
        }

        @media (max-width: 480px) {
            .design-container {
                width: 95vw;
                height: 95vw;
            }
            
            .content-area {
                padding: 25px 20px;
            }
            
            .imam-name {
                font-size: 1.5rem;
            }
            
            .condolence-text {
                font-size: 1rem;
            }
        }

        /* Islamic Decorative Elements */
        .decorative-corner {
            position: absolute;
            width: 40px;
            height: 40px;
            border: 2px solid #d4af37;
        }

        .corner-top-left {
            top: 25px;
            left: 25px;
            border-right: none;
            border-bottom: none;
        }

        .corner-top-right {
            top: 25px;
            right: 25px;
            border-left: none;
            border-bottom: none;
        }

        .corner-bottom-left {
            bottom: 25px;
            left: 25px;
            border-right: none;
            border-top: none;
        }

        .corner-bottom-right {
            bottom: 25px;
            right: 25px;
            border-left: none;
            border-top: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">ذكرى استشهاد الإمام محمد الباقر</h1>
        <p class="subtitle">عليه أفضل الصلاة والسلام</p>
    </div>

    <!-- Condolence Design -->
    <div class="design-container" id="condolenceDesign">
        <div class="islamic-pattern"></div>
        <div class="geometric-border"></div>

        <!-- Decorative Corners -->
        <div class="decorative-corner corner-top-left"></div>
        <div class="decorative-corner corner-top-right"></div>
        <div class="decorative-corner corner-bottom-left"></div>
        <div class="decorative-corner corner-bottom-right"></div>

        <div class="content-area">
            <div>
                <div class="imam-name">الإمام محمد الباقر</div>
                <div class="peace-phrase">عليه السلام</div>
                <div class="condolence-text">عظم الله أجوركم بذكرى استشهاد</div>
                <div class="condolence-text">باقر علوم الأولين والآخرين</div>
            </div>

            <div>
                <div class="prayer-text">اللهم صل على محمد وآل محمد</div>
                <div class="prayer-text">وعجل فرجهم الشريف</div>
            </div>
        </div>


    </div>

    <!-- Control Panel -->
    <div class="controls">
        <button class="btn btn-primary" onclick="downloadImage()">تحميل الصورة</button>
        <button class="btn btn-secondary" onclick="downloadImageFallback()">تحميل بديل</button>
        <button class="btn btn-secondary" onclick="downloadImageAlternative()">تحميل يدوي</button>
        <button class="btn btn-secondary" onclick="shareDesign()">مشاركة التصميم</button>
        <button class="btn btn-secondary" onclick="previewDesign()">معاينة</button>
        <button class="btn btn-secondary" onclick="debugCanvasCapture()" style="font-size: 0.8rem;">تشخيص</button>
    </div>

    <div class="loading" id="loadingIndicator">
        جاري تحضير الصورة...
        <div class="spinner"></div>
    </div>

    <!-- Hidden Canvas for Image Generation -->
    <canvas id="hiddenCanvas" style="display: none;"></canvas>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <script>
        // Enhanced image download functionality with comprehensive fixes
        async function downloadImage() {
            const loadingIndicator = document.getElementById('loadingIndicator');
            const designContainer = document.getElementById('condolenceDesign');

            try {
                // Show loading indicator
                loadingIndicator.style.display = 'block';
                showNotification('جاري تحضير الصورة...', 'info');

                // Ensure all fonts are loaded
                await document.fonts.ready;

                // Wait additional time for complete rendering
                await new Promise(resolve => setTimeout(resolve, 1000));

                // Scroll to the design container to ensure it's in viewport
                designContainer.scrollIntoView({ behavior: 'smooth', block: 'center' });
                await new Promise(resolve => setTimeout(resolve, 500));

                // Get container position and dimensions
                const rect = designContainer.getBoundingClientRect();
                console.log('Container dimensions:', rect);

                // Prepare the container for capture
                const originalTransform = designContainer.style.transform;
                const originalTransition = designContainer.style.transition;
                designContainer.style.transform = 'none';
                designContainer.style.transition = 'none';

                // Force a repaint
                designContainer.offsetHeight;

                // Configure html2canvas with optimized settings
                const options = {
                    scale: 2, // Reduced scale for better compatibility
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: '#000000',
                    width: 500, // Fixed width
                    height: 500, // Fixed height
                    scrollX: 0,
                    scrollY: 0,
                    windowWidth: window.innerWidth,
                    windowHeight: window.innerHeight,
                    foreignObjectRendering: false, // Disabled for better compatibility
                    removeContainer: false,
                    imageTimeout: 30000,
                    logging: true, // Enable logging for debugging
                    onclone: function(clonedDoc, element) {
                        console.log('Cloning document...');

                        // Find the cloned container
                        const clonedContainer = clonedDoc.getElementById('condolenceDesign');
                        if (clonedContainer) {
                            // Reset all transforms and ensure visibility
                            clonedContainer.style.position = 'relative';
                            clonedContainer.style.display = 'block';
                            clonedContainer.style.visibility = 'visible';
                            clonedContainer.style.opacity = '1';
                            clonedContainer.style.transform = 'none';
                            clonedContainer.style.transition = 'none';
                            clonedContainer.style.margin = '0';
                            clonedContainer.style.padding = '0';
                            clonedContainer.style.width = '500px';
                            clonedContainer.style.height = '500px';
                            clonedContainer.style.overflow = 'hidden';

                            // Ensure all child elements are visible
                            const allElements = clonedContainer.querySelectorAll('*');
                            allElements.forEach(el => {
                                el.style.visibility = 'visible';
                                el.style.opacity = '1';
                                el.style.transform = 'none';
                            });

                            // Force font loading in cloned document
                            const fontLink = clonedDoc.createElement('link');
                            fontLink.href = 'https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Scheherazade+New:wght@400;700&family=Noto+Sans+Arabic:wght@400;700&display=swap';
                            fontLink.rel = 'stylesheet';
                            clonedDoc.head.appendChild(fontLink);
                        }

                        return clonedDoc;
                    }
                };

                console.log('Starting html2canvas capture...');

                // Generate canvas from the design
                const canvas = await html2canvas(designContainer, options);

                console.log('Canvas generated:', canvas.width, 'x', canvas.height);

                // Restore original styles
                designContainer.style.transform = originalTransform;
                designContainer.style.transition = originalTransition;

                // Verify canvas has content
                if (canvas.width === 0 || canvas.height === 0) {
                    throw new Error('Canvas is empty - dimensions: ' + canvas.width + 'x' + canvas.height);
                }

                // Create a new canvas with exact dimensions if needed
                const finalCanvas = document.createElement('canvas');
                finalCanvas.width = 500 * 2; // 2x scale
                finalCanvas.height = 500 * 2;
                const ctx = finalCanvas.getContext('2d');

                // Fill background
                ctx.fillStyle = '#000000';
                ctx.fillRect(0, 0, finalCanvas.width, finalCanvas.height);

                // Draw the captured image
                ctx.drawImage(canvas, 0, 0, finalCanvas.width, finalCanvas.height);

                // Create download link with high quality
                const link = document.createElement('a');
                link.download = 'الإمام_محمد_الباقر_عليه_السلام_تعزية.png';
                link.href = finalCanvas.toDataURL('image/png', 1.0);

                // Trigger download
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                // Show success message with canvas info
                showNotification(`تم تحميل الصورة بنجاح! (${finalCanvas.width}x${finalCanvas.height})`, 'success');

            } catch (error) {
                console.error('Error generating image:', error);
                showNotification('حدث خطأ في تحميل الصورة. جاري المحاولة بطريقة بديلة...', 'error');

                // Automatically try fallback method
                setTimeout(() => {
                    downloadImageFallback();
                }, 1000);

            } finally {
                // Hide loading indicator
                loadingIndicator.style.display = 'none';
            }
        }

        // Improved fallback download method
        async function downloadImageFallback() {
            const loadingIndicator = document.getElementById('loadingIndicator');
            const designContainer = document.getElementById('condolenceDesign');

            try {
                loadingIndicator.style.display = 'block';
                showNotification('جاري المحاولة بالطريقة البديلة...', 'info');

                // Wait for fonts
                await document.fonts.ready;
                await new Promise(resolve => setTimeout(resolve, 2000));

                // Create a completely isolated container for capture
                const captureContainer = document.createElement('div');
                captureContainer.innerHTML = designContainer.outerHTML;
                captureContainer.style.position = 'absolute';
                captureContainer.style.top = '0';
                captureContainer.style.left = '0';
                captureContainer.style.width = '500px';
                captureContainer.style.height = '500px';
                captureContainer.style.zIndex = '9999';
                captureContainer.style.background = 'linear-gradient(135deg, #000000 0%, #1a1a2e 50%, #0f3460 100%)';
                captureContainer.style.visibility = 'visible';
                captureContainer.style.opacity = '1';

                // Add to body temporarily
                document.body.appendChild(captureContainer);

                // Get the inner design container
                const innerDesign = captureContainer.querySelector('#condolenceDesign');
                if (innerDesign) {
                    innerDesign.id = 'tempDesignContainer';
                    innerDesign.style.position = 'relative';
                    innerDesign.style.transform = 'none';
                    innerDesign.style.transition = 'none';
                }

                // Wait for rendering
                await new Promise(resolve => setTimeout(resolve, 1000));

                // Simple but effective options
                const options = {
                    scale: 1.5,
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: '#000000',
                    width: 500,
                    height: 500,
                    logging: false,
                    foreignObjectRendering: false
                };

                const canvas = await html2canvas(innerDesign || captureContainer, options);

                // Remove temporary container
                document.body.removeChild(captureContainer);

                if (canvas.width === 0 || canvas.height === 0) {
                    throw new Error('Fallback canvas is also empty');
                }

                const link = document.createElement('a');
                link.download = 'الإمام_محمد_الباقر_عليه_السلام_تعزية_بديل.png';
                link.href = canvas.toDataURL('image/png', 1.0);

                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                showNotification(`تم تحميل الصورة بالطريقة البديلة! (${canvas.width}x${canvas.height})`, 'success');

            } catch (error) {
                console.error('Fallback method failed:', error);
                showNotification('فشلت جميع طرق التحميل. يرجى تجربة متصفح آخر.', 'error');

                // Show the alternative download button
                if (window.showAlternativeDownload) {
                    window.showAlternativeDownload();
                }
            } finally {
                loadingIndicator.style.display = 'none';
            }
        }

        // Share functionality
        async function shareDesign() {
            if (navigator.share) {
                try {
                    await navigator.share({
                        title: 'ذكرى استشهاد الإمام محمد الباقر عليه السلام',
                        text: 'عظم الله أجوركم بذكرى استشهاد باقر علوم الأولين والآخرين',
                        url: window.location.href
                    });
                } catch (error) {
                    console.log('Error sharing:', error);
                    copyToClipboard();
                }
            } else {
                copyToClipboard();
            }
        }

        // Copy URL to clipboard
        function copyToClipboard() {
            navigator.clipboard.writeText(window.location.href).then(() => {
                showNotification('تم نسخ الرابط إلى الحافظة!', 'success');
            }).catch(() => {
                showNotification('لم يتم نسخ الرابط. يرجى النسخ يدوياً.', 'error');
            });
        }

        // Preview functionality
        function previewDesign() {
            const designContainer = document.getElementById('condolenceDesign');

            // Create modal for preview
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.9);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 1000;
                cursor: pointer;
            `;

            const previewImage = designContainer.cloneNode(true);
            previewImage.style.cssText = `
                transform: scale(1.2);
                box-shadow: 0 20px 60px rgba(212, 175, 55, 0.3);
            `;

            modal.appendChild(previewImage);
            document.body.appendChild(modal);

            // Close modal on click
            modal.addEventListener('click', () => {
                document.body.removeChild(modal);
            });

            // Close modal on escape key
            const handleEscape = (e) => {
                if (e.key === 'Escape') {
                    document.body.removeChild(modal);
                    document.removeEventListener('keydown', handleEscape);
                }
            };
            document.addEventListener('keydown', handleEscape);
        }

        // Notification system
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 20px;
                border-radius: 8px;
                color: white;
                font-family: 'Noto Sans Arabic', sans-serif;
                font-weight: 600;
                z-index: 1001;
                transform: translateX(100%);
                transition: transform 0.3s ease;
                max-width: 300px;
                text-align: right;
                direction: rtl;
            `;

            // Set background color based on type
            switch (type) {
                case 'success':
                    notification.style.background = 'linear-gradient(135deg, #28a745, #20c997)';
                    break;
                case 'error':
                    notification.style.background = 'linear-gradient(135deg, #dc3545, #e74c3c)';
                    break;
                default:
                    notification.style.background = 'linear-gradient(135deg, #d4af37, #b8860b)';
                    notification.style.color = '#000';
            }

            notification.textContent = message;
            document.body.appendChild(notification);

            // Animate in
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);

            // Remove after 3 seconds
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Add smooth animations
            const designContainer = document.getElementById('condolenceDesign');
            designContainer.style.opacity = '0';
            designContainer.style.transform = 'translateY(20px)';

            setTimeout(() => {
                designContainer.style.transition = 'all 0.8s ease';
                designContainer.style.opacity = '1';
                designContainer.style.transform = 'translateY(0)';
            }, 300);

            // Add keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey || e.metaKey) {
                    switch (e.key) {
                        case 's':
                            e.preventDefault();
                            downloadImage();
                            break;
                        case 'p':
                            e.preventDefault();
                            previewDesign();
                            break;
                    }
                }
            });

            // Ensure fonts are loaded before showing notification
            document.fonts.ready.then(() => {
                showNotification('مرحباً بكم في تصميم تعزية الإمام محمد الباقر عليه السلام', 'info');
            });

            // Add alternative download method button for testing
            addAlternativeDownloadButton();
        });

        // Add alternative download method for testing
        function addAlternativeDownloadButton() {
            const controls = document.querySelector('.controls');
            const altButton = document.createElement('button');
            altButton.className = 'btn btn-secondary';
            altButton.textContent = 'تحميل بديل';
            altButton.onclick = downloadImageAlternative;
            altButton.style.display = 'none'; // Hidden by default
            controls.appendChild(altButton);

            // Show alternative button if main download fails
            window.showAlternativeDownload = function() {
                altButton.style.display = 'inline-block';
            };
        }

        // Alternative download method using Canvas API directly
        async function downloadImageAlternative() {
            const loadingIndicator = document.getElementById('loadingIndicator');
            const designContainer = document.getElementById('condolenceDesign');

            try {
                loadingIndicator.style.display = 'block';
                showNotification('جاري التحميل بالطريقة الثالثة...', 'info');

                // Create canvas manually
                const canvas = document.createElement('canvas');
                canvas.width = 1000; // 2x resolution
                canvas.height = 1000;
                const ctx = canvas.getContext('2d');

                // Set high quality rendering
                ctx.imageSmoothingEnabled = true;
                ctx.imageSmoothingQuality = 'high';

                // Draw background gradient
                const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                gradient.addColorStop(0, '#000000');
                gradient.addColorStop(0.5, '#1a1a2e');
                gradient.addColorStop(1, '#0f3460');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // Draw border
                ctx.strokeStyle = '#d4af37';
                ctx.lineWidth = 6;
                ctx.strokeRect(15, 15, canvas.width - 30, canvas.height - 30);

                // Draw inner border
                ctx.strokeStyle = '#d4af37';
                ctx.lineWidth = 4;
                ctx.strokeRect(45, 45, canvas.width - 90, canvas.height - 90);

                // Draw decorative corners
                const cornerSize = 80;
                const cornerOffset = 75;
                ctx.strokeStyle = '#d4af37';
                ctx.lineWidth = 4;

                // Top-left corner
                ctx.beginPath();
                ctx.moveTo(cornerOffset, cornerOffset + cornerSize);
                ctx.lineTo(cornerOffset, cornerOffset);
                ctx.lineTo(cornerOffset + cornerSize, cornerOffset);
                ctx.stroke();

                // Top-right corner
                ctx.beginPath();
                ctx.moveTo(canvas.width - cornerOffset - cornerSize, cornerOffset);
                ctx.lineTo(canvas.width - cornerOffset, cornerOffset);
                ctx.lineTo(canvas.width - cornerOffset, cornerOffset + cornerSize);
                ctx.stroke();

                // Bottom-left corner
                ctx.beginPath();
                ctx.moveTo(cornerOffset, canvas.height - cornerOffset - cornerSize);
                ctx.lineTo(cornerOffset, canvas.height - cornerOffset);
                ctx.lineTo(cornerOffset + cornerSize, canvas.height - cornerOffset);
                ctx.stroke();

                // Bottom-right corner
                ctx.beginPath();
                ctx.moveTo(canvas.width - cornerOffset - cornerSize, canvas.height - cornerOffset);
                ctx.lineTo(canvas.width - cornerOffset, canvas.height - cornerOffset);
                ctx.lineTo(canvas.width - cornerOffset, canvas.height - cornerOffset - cornerSize);
                ctx.stroke();

                // Set text properties
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillStyle = '#d4af37';

                // Draw main title
                ctx.font = 'bold 64px Amiri, serif';
                ctx.fillText('الإمام محمد الباقر', canvas.width / 2, 300);

                // Draw peace phrase
                ctx.font = '32px Scheherazade New, serif';
                ctx.fillStyle = '#b8860b';
                ctx.fillText('عليه السلام', canvas.width / 2, 370);

                // Draw condolence text
                ctx.font = '38px Noto Sans Arabic, sans-serif';
                ctx.fillStyle = '#ffffff';
                ctx.fillText('عظم الله أجوركم بذكرى استشهاد', canvas.width / 2, 500);
                ctx.fillText('باقر علوم الأولين والآخرين', canvas.width / 2, 560);

                // Draw prayer text
                ctx.font = 'italic 28px Amiri, serif';
                ctx.fillStyle = '#d4af37';
                ctx.fillText('اللهم صل على محمد وآل محمد', canvas.width / 2, 700);
                ctx.fillText('وعجل فرجهم الشريف', canvas.width / 2, 750);

                // Create download link
                const link = document.createElement('a');
                link.download = 'الإمام_محمد_الباقر_عليه_السلام_تعزية_يدوي.png';
                link.href = canvas.toDataURL('image/png', 1.0);

                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                showNotification('تم تحميل الصورة بالطريقة اليدوية بنجاح!', 'success');

            } catch (error) {
                console.error('Manual canvas method failed:', error);
                showNotification('فشل التحميل اليدوي أيضاً', 'error');
            } finally {
                loadingIndicator.style.display = 'none';
            }
        }

        // Debug function to test canvas capabilities
        function debugCanvasCapture() {
            const designContainer = document.getElementById('condolenceDesign');
            console.log('=== DEBUG INFO ===');
            console.log('Container:', designContainer);
            console.log('Container dimensions:', designContainer.getBoundingClientRect());
            console.log('Container computed style:', window.getComputedStyle(designContainer));
            console.log('Fonts loaded:', document.fonts.status);
            console.log('Available fonts:', Array.from(document.fonts).map(f => f.family));

            // Test simple canvas creation
            const testCanvas = document.createElement('canvas');
            testCanvas.width = 100;
            testCanvas.height = 100;
            const testCtx = testCanvas.getContext('2d');
            testCtx.fillStyle = 'red';
            testCtx.fillRect(0, 0, 100, 100);
            console.log('Test canvas data URL length:', testCanvas.toDataURL().length);

            showNotification('معلومات التشخيص متوفرة في وحدة التحكم', 'info');
        }

        // Enhanced responsive handling
        function handleResize() {
            const designContainer = document.getElementById('condolenceDesign');
            const containerWidth = Math.min(window.innerWidth - 40, 500);

            if (window.innerWidth <= 768) {
                designContainer.style.width = containerWidth + 'px';
                designContainer.style.height = containerWidth + 'px';
            }
        }

        window.addEventListener('resize', handleResize);
        window.addEventListener('load', handleResize);
    </script>
</body>
</html>
